{% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Favicons -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="icon" type="image/svg+xml">
    <link href="{% static 'users/assets/img/apple-touch-icon.svg' %}" rel="apple-touch-icon">
    <!-- Fallback for older browsers -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="shortcut icon">

    <title>Receipt</title>
    <link rel="stylesheet" href="{% static 'users/css/style.css' %}" />
    <style>
      /* General Styles */
      body {
        background-color: #f8fafc;
        font-family: 'Roboto', Arial, sans-serif;
        color: #333;
        margin: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }
      
      .receipt {
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        max-width: 500px;
        padding: 30px;
        margin: auto;
      }
      
      /* Header Section */
      .header {
        display: flex;
        align-items: center;
        border-bottom: 2px dashed #ccc;
        padding-bottom: 20px;
        margin-bottom: 20px;
      }
      
      .header .logo-container {
        margin-right: 20px;
        flex-shrink: 0;
      }
      
      .header img {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        object-fit: cover;
      }
      
      .header .company-info {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      
      .header .company-info h2 {
        margin: 0;
        font-size: 1.5rem;
        color: rgb(6, 45, 87);
        font-weight: 700;
      }
      
      .header .company-info p {
        margin: 4px 0;
        font-size: 0.85rem;
        color: #555;
      }
      
      /* Receipt Title */
      h3 {
        text-align: center;
        font-size: 1.3rem;
        margin: 10px 0 20px;
        color: #28a745;
        text-transform: uppercase;
        letter-spacing: 1px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
      }
      
      h3 .verified-icon {
        color: #28a745;
        font-size: 1.5rem;
      }
      
      /* Receipt Content */
      .receipt-content {
        font-size: 0.95rem;
      }
      
      .receipt-content p {
        margin: 10px 0;
      }
      
      .receipt-content strong {
        font-weight: bold;
        display: inline-block;
        width: 150px;
        color: #555;
      }
      
      .receipt-content .secondary-text {
        font-size: 0.85rem;
        color: #555; /* Softer color for secondary text */
        margin-top: 6px;
      }
      
      table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
      }
      
      th,
      td {
        text-align: left;
        padding: 10px;
        border: 1px solid #ddd;
      }
      
      th {
        background-color: #f1f3f5;
        color: #555;
        font-size: 0.9rem;
      }
      
      td {
        font-size: 0.9rem;
      }
      
      /* Total Section */
      .total {
        text-align: right;
        margin-top: 20px;
        font-size: 1.1rem;
        font-weight: bold;
      }
      
      .total strong {
        display: inline-block;
        width: auto;
        color: #000;
      }
      
      /* Download Button */
      .download-btn {
        display: block;
        margin: 20px auto 10px;
        text-align: center;
        padding: 12px 20px;
        background: rgb(6, 45, 87);
        color: #fff;
        border: none;
        border-radius: 5px;
        font-size: 1rem;
        text-decoration: none;
        font-weight: bold;
        transition: all 0.3s ease;
        cursor: pointer;
      }
      
      .download-btn:hover {
        background: rgb(0, 45, 93);
      }
      
      /* Back to Home Button */
      .back-home-btn {
        display: block;
        margin: 10px auto;
        text-align: center;
        padding: 12px 20px;
        background: #28a745;
        color: #fff;
        border: none;
        border-radius: 5px;
        font-size: 1rem;
        text-decoration: none;
        font-weight: bold;
        transition: all 0.3s ease;
        cursor: pointer;
      }
      
      .back-home-btn:hover {
        background: #218838;
      }
      
      .thank-you {
        text-align: center;
        margin-top: 20px;
        font-size: 0.9rem;
        font-style: italic;
        color: #555;
      }
      
      /* Responsive Design */
      @media (max-width: 450px) {
        .receipt {
          padding: 20px;
        }
      
        .header {
          flex-direction: column;
          align-items: center;
          text-align: center;
        }
      
        .header .logo-container {
          margin-bottom: 10px;
        }
      
        .header .company-info {
          align-items: center;
        }
      
        .header img {
          width: 80px;
          height: 80px;
        }
      }
    </style>
  </head>
  <body>
    <div class="receipt">
      <div class="header">
        <div class="logo-container">
          <img src="{{ receipt_data.company_logo }}" alt="Company Logo" />
        </div>
        <div class="company-info">
          <h2>{{ receipt_data.company_name }}</h2>
          <p>{{ receipt_data.company_address }}</p>
          <p>WhatsApp: {{ receipt_data.company_contact }}</p>
        </div>
      </div>
      <h3>
        <i class="verified-icon">&#10004;</i>
        Payment Confirmed
      </h3>

      <div class="receipt-content">
        <p>
          <strong>Customer Name:</strong> {{ receipt_data.customer_name }}
        </p>
        <table>
          <thead>
            <tr>
              <th>Product</th>
              <th>Quantity</th>
              <th>Price</th>
              <th>Subtotal</th>
            </tr>
          </thead>
          <tbody>
            {% for product in receipt_data.products %}
              <tr>
                <td>{{ product.product_bought }}</td>
                <td>{{ product.quantity }}</td>
                <td>#{{ product.price }}</td>
                <td>#{{ product.subtotal|floatformat:2 }}</td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
        {% if receipt_data.discount %}
          <p class="secondary-text">Discount: #{{ receipt_data.discount }}</p>
        {% endif %}
        <p class="secondary-text">Date of Purchase: {{ receipt_data.date_of_purchase }}</p>
        <p class="secondary-text">Method of Purchase: {{ receipt_data.method_of_purchase }}</p>
        <p class="total">
          <strong>Total:</strong> #{{ receipt_data.total }}
        </p>
      </div>

      <a href="#" id="download-btn" class="download-btn">Download Receipt</a>
      <!-- Back to Home Button -->
      <a href="{% url 'welcome' %}" class="back-home-btn">Back to Home</a>
      <p class="thank-you">Thank you {{ receipt_data.customer_name }} for your patronage!</p>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script>
      document.getElementById('download-btn').onclick = function (event) {
        event.preventDefault()
        const receiptElement = document.querySelector('.receipt')
        const downloadButton = document.getElementById('download-btn')
        const backHomeButton = document.querySelector('.back-home-btn') // Select the back home button
        downloadButton.style.display = 'none'
        backHomeButton.style.display = 'none' // Hide the back home button
      
        // Use html2canvas to capture the screenshot without the back home button
        html2canvas(receiptElement).then((canvas) => {
          const link = document.createElement('a')
          link.download = 'receipt.png'
          link.href = canvas.toDataURL()
          link.click()
      
          // After download, restore the button visibility
          downloadButton.style.display = 'block'
          backHomeButton.style.display = 'block' // Show the back home button again
        })
      }
    </script>
  </body>
</html>
