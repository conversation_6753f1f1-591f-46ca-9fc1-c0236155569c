{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuickReceipt Dashboard</title>

    <!-- Favicons -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="icon" type="image/svg+xml">
    <link href="{% static 'users/assets/img/apple-touch-icon.svg' %}" rel="apple-touch-icon">
    <!-- Fallback for older browsers -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="shortcut icon">

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f9f9fb;
            font-family: 'Arial', sans-serif;
        }

        .sidebar {
            width: 250px;
            background-color: #ffffff;
            border-right: 1px solid #eaeaea;
            height: 100vh;
            position: fixed;
            padding: 20px;
        }

        .sidebar a {
            display: block;
            color: #333;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .sidebar a.active, .sidebar a:hover {
            background-color: #007bff;
            color: #ffffff;
        }

        .content {
            margin-left: 270px;
            padding: 20px;
        }

        .table-container {
            background: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .table th, .table td {
            vertical-align: middle;
        }

        .filter-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .filter-section input, .filter-section select {
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 8px;
        }

        .header {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        /* Hide filter section on small screens */
        @media (max-width: 991px) {
            .filter-section {
                display: none;
            }
        }

        /* Hide sidebar for smaller screens and show toggle button */
        @media (max-width: 991px) {
            .sidebar {
                display: none;
            }
            .content {
                margin-left: 0;
            }
            .navbar-toggler {
                display: block;
            }
        }

        @media (min-width: 992px) {
            .navbar-toggler {
                display: none;
            }
        }

        .navbar-collapse {
            flex-grow: 1;
            justify-content: flex-end;
        }

        .navbar-nav .nav-item {
            margin-left: 15px;
        }
    </style>
</head>
<body>
    <!-- Navbar with Hamburger Icon -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'welcome' %}">Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'generate_receipt' %}">Generate Receipt</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'update_profile' %}">Profile</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">Settings</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">Logout</a>
                </li>
            </ul>
        </div>
    </nav>

    <div class="sidebar">
        <h2 class="mb-4">QuickReceipt</h2>
        <a href="{% url 'welcome' %}" class="active">Dashboard</a>
        <a href="{% url 'generate_receipt' %}">Generate Receipt</a>
        <a href="{% url 'update_profile' %}">Profile</a>
        <a href="#">Settings</a>
        <a href="#">Logout</a>
    </div>

    <div class="content">
        <div class="header">Welcome to your dashboard, {{ username }}</div>
        
        <!-- Filter Section Hidden on Mobile -->
        <div class="filter-section">
            <div>
                <label for="search" class="me-2">Search:</label>
                <input type="text" id="search" placeholder="Search receipts...">
            </div>
            <div>
                <label for="date-from" class="me-2">From:</label>
                <input type="date" id="date-from">

                <label for="date-to" class="ms-3 me-2">To:</label>
                <input type="date" id="date-to">

                <button class="btn btn-primary ms-3">Filter</button>
            </div>
        </div>

        <div class="table-container">
            <p class="mb-3">Receipt generation history</p>
            {% if receipts %}
            <table class="table table-hover">
                <thead class="table-light">
                <tr>
                    <th>S/N</th>
                    <th>Customer Name</th>
                    <th>Payment Method</th>
                    <th>Date</th>
                    <th>Total</th>
                    <th>Action</th>
                </tr>
                </thead>
                <tbody>
                    {% for receipt in receipts %}
                    <tr>
                        <td>{{ forloop.counter0|add:receipts.start_index }}</td>
                        <td>{{ receipt.customer_name }}</td>
                        <td>{{ receipt.payment_method }}</td>
                        <td>{{ receipt.date }}</td>
                        <td>#{{ receipt.total }}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary">View</button>
                            <button class="btn btn-sm btn-outline-danger">Delete</button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center">No receipts found.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p class="text-center">No receipts generated yet</p>
            {% endif %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
