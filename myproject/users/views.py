from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from .forms import SignUpForm, UpdateProfileForm, ReceiptForm
import json
from .models import Receipt, UserProfile
from django.core.paginator import Paginator
from django.contrib.auth import authenticate, login
from django.urls import reverse


def signup_view(request):
    if request.method == "POST":
        form = SignUpForm(request.POST)
        if form.is_valid():
            form.save()
            return redirect("login")  # Redirect to login page after successful sign-up
        else:
            return render(
                request, "users/signup.html", {"form": form, "errors": form.errors}
            )
    else:
        form = SignUpForm()

    return render(request, "users/signup.html", {"form": form})


def login_view(request):
    if request.method == "POST":
        username = request.POST["username"]
        password = request.POST["password"]
        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)  # Log the user in
            print("login successful")
            return redirect(
                "welcome"
            )  # Redirect to the welcome page after successful login
        else:
            # Invalid login credentials
            print("invalid credentials")
            return render(
                request, "users/login.html", {"errors": "Invalid username or password"}
            )

    return render(request, "users/login.html", {"error": None})


def home_view(request):
    return render(request, "users/home.html")


def welcome_view(request):
    username = request.user.username  # Get the username of the logged-in user
    return render(request, "users/welcome.html", {"username": username})


def update_profile(request):
    user_profile, created = UserProfile.objects.get_or_create(
        user=request.user
    )  # Get the current user's profile

    if request.method == "POST":
        form = UpdateProfileForm(request.POST, request.FILES)
        if form.is_valid():
            # Check if company logo is provided
            if not form.cleaned_data["company_logo"]:
                form.add_error("company_logo", "Company logo is required.")
                return render(request, "users/update_profile.html", {"form": form})

            # Update the user's profile with the new data
            user_profile.company_logo = form.cleaned_data["company_logo"]
            user_profile.company_name = form.cleaned_data["company_name"]
            user_profile.company_details = form.cleaned_data["company_details"]
            user_profile.address = form.cleaned_data["address"]
            user_profile.whatsapp_contact = form.cleaned_data["whatsapp_contact"]
            user_profile.save()  # Save the profile with updated information
            next_url = request.GET.get("next")
            if next_url:
                return redirect(next_url)
            return redirect("welcome")
    else:
        # Populate the form with current user's profile data
        form = UpdateProfileForm(
            initial={
                "company_logo": user_profile.company_logo,
                "company_name": user_profile.company_name,
                "company_details": user_profile.company_details,
                "address": user_profile.address,
                "whatsapp_contact": user_profile.whatsapp_contact,
            }
        )

    return render(request, "users/update_profile.html", {"form": form})


@login_required
def generate_receipt(request):
    user_profile, created = UserProfile.objects.get_or_create(user=request.user)
    if not user_profile.company_name:
        return redirect(f"{reverse('update_profile')}?next=generate_receipt")

    if request.method == "POST":
        form = ReceiptForm(request.POST)
        if form.is_valid():
            # Extract form data
            customer_name = form.cleaned_data["customer_name"]
            products_json = form.cleaned_data["products"]
            discount = form.cleaned_data["discount"]
            date_of_purchase = form.cleaned_data["date_of_purchase"]
            method_of_purchase = form.cleaned_data["method_of_purchase"]

            # Parse products JSON
            products = json.loads(products_json)

            # Calculate subtotal for each product and total
            for product in products:
                product["subtotal"] = product["quantity"] * product["price"]
            total = sum(p["subtotal"] for p in products) - float(discount)
            # save the data into the database
            receipt = Receipt.objects.create(
                user=request.user,
                customer_name=customer_name,
                products=products,
                discount=discount,
                total=total,
                payment_method=method_of_purchase,
            )
            receipt.save()

            # Prepare data for rendering receipt
            receipt_data = {
                "customer_name": customer_name,
                "company_address": user_profile.address,
                "company_contact": user_profile.whatsapp_contact,
                "products": products,
                "discount": discount if discount > 0 else "",
                "total": total,
                "date_of_purchase": date_of_purchase,
                "method_of_purchase": method_of_purchase,
                "company_logo": user_profile.company_logo.url,
                "company_name": user_profile.company_name,
            }
            return render(request, "users/receipt.html", {"receipt_data": receipt_data})
    else:
        form = ReceiptForm()
    return render(request, "users/generate_receipt.html", {"form": form})


def dashboard(request):
    """displays the dashboard for the user"""
    try:
        receipts = Receipt.objects.filter(user=request.user).order_by("-date")
        paginator = Paginator(receipts, 10)
        page_number = request.GET.get("page", 1)
        page_obj = paginator.get_page(page_number)

        return render(
            request,
            "users/dashboard.html",
            {"receipts": page_obj, "username": request.user.username},
        )

    except Receipt.DoesNotExist:
        return None
    except Exception as e:
        print(f"exception: {str(e)}")
        return render(request, "users/welcome.html")
